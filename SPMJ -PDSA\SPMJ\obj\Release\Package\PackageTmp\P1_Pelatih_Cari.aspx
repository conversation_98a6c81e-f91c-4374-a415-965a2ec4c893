﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_Pelatih_Cari.aspx.vb" Inherits="SPMJ.WebForm3" 
    title="SPMJ" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 28px;
        }
        .style2
        {
            height: 3px;
        }
        .style3
        {
            height: 10px;
        }
        .style4
        {
            width: 720px;
        }
        .style5
        {
            height: 28px;
            width: 720px;
        }
        .style6
        {
            height: 10px;
            width: 720px;
        }
        .style7
        {
            height: 3px;
            width: 720px;
        }
        .style8
        {
            width: 172px;
        }
        .style9
        {
            height: 28px;
            width: 172px;
        }
        .style10
        {
            height: 10px;
            width: 172px;
        }
        .style11
        {
            height: 3px;
            width: 172px;
        }
    </style>
    
<script src="http://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" type="text/javascript"></script>
<script src="http://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js" type="text/javascript"></script>
<link href="http://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="Stylesheet" type="text/css" />

<script type="text/javascript">
        // if you use jQuery, you can load them when dom is read.
        $(document).ready(function() {
            var prm = Sys.WebForms.PageRequestManager.getInstance();
            prm.add_initializeRequest(InitializeRequest);
            prm.add_endRequest(EndRequest);

             $("#<%=Cb_Kolej.ClientID%>").select2({
              placeholder: "Pilih  Insitusi Pengajian",
              allowClear: true,
               width: 'resolve'
          });

        });

        function InitializeRequest(sender, args) {
             $("#<%=Cb_Kolej.ClientID%>").select2({
              placeholder: "Pilih  Insitusi Pengajian",
              allowClear: true,
               width: 'resolve'
          });

        }

        function EndRequest(sender, args) {
            $("#<%=Cb_Kolej.ClientID%>").select2({
           placeholder: "Pilih  Insitusi Pengajian",
              allowClear: true,
               width: 'resolve'
          });
        }
  </script>
      
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
    
        <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style8"></td>
            <td class="style4"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style8">&nbsp;</td>
            <td class="style4">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style9"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#5D7B9D" class="style5">Pinda/Semak Rekod Pelatih - Carian Rekod Pelatih</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td 
                
                style="border-width: 1px; border-right-style: solid; border-left-style: solid; border-color: #5D7B9D" 
                bgcolor="White" class="style6">&nbsp;&nbsp;</td>
            <td class="style3"></td>
        </tr>
        <tr>
            <td class="style8"></td>
            <td 
                
                style="border-width: 1px; border-right-style: solid; border-left-style: solid; border-color: #5D7B9D" 
                bgcolor="White" class="style4">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">KOLEJ/INSTITUSI</asp:TextBox>
                       <asp:DropDownList ID="Cb_Kolej" runat="server"  AutoPostBack="True" 
                            Font-Names="Arial" Font-Size="8pt" Width="520px" >
                        </asp:DropDownList>
                        
                          
                        <br />                  
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">JENIS KURSUS</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kursus" runat="server" AutoPostBack="True" 
                            Font-Names="Arial" Font-Size="8pt" Width="300px">
                        </asp:DropDownList>
                        
                        <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">SESI PENGAMBILAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Sesi" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="156px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
&nbsp;</td>
            <td align="center"></td>
        </tr>
        <tr>
            <td class="style11"></td>
            <td 
                
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style7">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj2" 
                    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="16px" 
            tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="150px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style8">&nbsp;</td>
            <td 
                             
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style4">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">NO. KP/TENTERA/PASPORT</asp:TextBox>
                <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="150px" 
                    Wrap="False"></asp:TextBox>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style8">&nbsp;</td>
            <td 
                             
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style4">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="172px"></asp:TextBox>
                <asp:Button ID="cmdHantar1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td 
                
                style="border-bottom-style: solid; border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style6">&nbsp;&nbsp;</td>
            <td class="style3"></td>
        </tr>
    
    
        <tr>
            <td class="style8">&nbsp;</td>
            <td class="style4">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
    
    
        <tr>
            <td class="style8">&nbsp;</td>
            <td class="style4">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" Visible="False" BorderColor="#5D7B9D">
                    <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="#F7F6F3" />
                    <Columns>
                        <asp:TemplateField ShowHeader="False">
                            <ItemTemplate>
                                <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="PILIH" Width="60px" />
                            </ItemTemplate>
                            <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                            <HeaderStyle Width="65px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
            <td>&nbsp;</td>
        </tr>
    
    
        <tr>
            <td class="style8">&nbsp;</td>
            <td class="style4">
                <br />
                <br />
                <asp:Button ID="cmd_Word" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXPORT KE WORD" Width="150px" 
                    Visible="False" />
                &nbsp;<asp:Button ID="cmd_Excel" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXPORT KE EXCEL" Width="150px" 
                    Visible="False" />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
   
    </div>
</asp:Content>
