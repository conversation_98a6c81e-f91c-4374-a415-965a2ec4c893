<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="p0_PasswordChangeForced.aspx.vb" Inherits="SPMJ.p0_PasswordChangeForced" 
    title="SPMJ - Password Change Required" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
    .style1
    {
        width: 214px;
    }
    .style2
    {
        width: 144px;
    }
        .style3
        {
            height: 28px;
        }
        .style4
        {
            width: 144px;
            height: 28px;
        }
        .style5
        {
            width: 214px;
            height: 28px;
        }
        .password-requirements {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            color: #856404;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="width: 100%; height: 100%;">
        <table style="width: 100%; height: 100%;">
            <tr>
                <td align="center" title="SPMJ - Password Change Required">
                    <br />
                    <div class="security-notice">
                        <strong>Kemaskini Keselamatan Diperlukan</strong><br />
                        Untuk meningkatkan keselamatan sistem, anda dikehendaki menetapkan kata laluan baru yang selamat.
                        Kata laluan anda kini akan disulitkan untuk perlindungan yang lebih baik.
                    </div>
                    <br />
                    <table cellpadding="-1" cellspacing="-1" 
                        style="width: 100%; font-family: Arial; font-size: 8pt; font-variant: small-caps;">
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td bgcolor="White" class="style2" 
                                style="border-left-style: solid; border-top-style: solid; border-width: 1px; border-color: #000000">
                                &nbsp;</td>
                            <td bgcolor="White" class="style1" 
                                style="border-width: 1px; border-color: #000000; border-top-style: solid; border-right-style: solid">
                                &nbsp;</td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td class="style3">
                                </td>
                            <td align="center" bgcolor="#719548" class="style4" 
                                style="border-left-style: solid; border-left-width: 1px; border-color: #000000; color: #FFFFFF; font-weight: bold;">
                                TETAPKAN KATA LALUAN BARU
                            </td>
                            <td align="center" bgcolor="#719548" class="style5" 
                                style="border-color: #000000; border-right-style: solid; border-right-width: 1px; color: #FFFFFF; font-weight: bold;">
                                &nbsp;
                            </td>
                            <td class="style3">
                                </td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td align="right" bgcolor="White" class="style2" 
                                style="border-left-style: solid; border-left-width: 1px; border-color: #000000">
                                Kata Laluan Baru :&nbsp;
                            </td>
                            <td align="left" bgcolor="White" class="style1" 
                                style="border-color: #000000; border-right-style: solid; border-right-width: 1px">
                <asp:TextBox ID="Tx_NewPwd" runat="server" CssClass="std" Width="150px" 
                    Wrap="False" MaxLength="20" TextMode="Password"></asp:TextBox>
                            </td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td align="right" bgcolor="White" class="style2" 
                                style="border-left-style: solid; border-left-width: 1px; border-color: #000000">
                                Sahkan Kata Laluan :&nbsp;
                            </td>
                            <td align="left" bgcolor="White" class="style1" 
                                style="border-color: #000000; border-right-style: solid; border-right-width: 1px">
                <asp:TextBox ID="Tx_ConfirmPwd" runat="server" CssClass="std" Width="150px" 
                    Wrap="False" MaxLength="20" TextMode="Password"></asp:TextBox>
                <div class="password-requirements">
                    Kata laluan mestilah sekurang-kurangnya 6 aksara
                </div>
                            </td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td bgcolor="White" class="style2" 
                                style="border-left-style: solid; border-left-width: 1px; border-color: #000000">
                                &nbsp;</td>
                            <td align="left" bgcolor="White" class="style1" 
                                style="border-color: #000000; border-right-style: solid; border-right-width: 1px; font-family: Arial; font-size: 3px;">
                                <br />
                <asp:Button ID="cmd_Update" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="KEMASKINI" Width="80px" />
                &nbsp;
                <asp:Button ID="cmd_Logout" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="4" Text="LOG KELUAR" Width="80px" />
                            </td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td bgcolor="White" class="style2" 
                                style="border-bottom-style: solid; border-left-style: solid; border-bottom-width: 1px; border-left-width: 1px; border-color: #000000">
                                &nbsp;</td>
                            <td align="left" bgcolor="White" class="style1" 
                                style="border-color: #000000; border-right-style: solid; border-right-width: 1px; border-bottom-style: solid; border-bottom-width: 1px;">
                                &nbsp;</td>
                            <td>
                                &nbsp;</td>
                        </tr>
                    </table>
                    <br />
                    <br />
                </td>
            </tr>
        </table>

    </div></asp:Content>
